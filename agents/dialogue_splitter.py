"""
Dialogue Splitter Agent
----------------------
Parses the Hindi story JSO<PERSON> to extract scene dialogues/cues.
"""

import os
import json
import logging
from typing import List

from langchain_openai import ChatOpenAI
from crewai import Agent, Task, Crew, Process

from models.schema import Story, SceneSegment
from utils.parsers import SceneSegmentListParser

logger = logging.getLogger(__name__)

class DialogueSplitterAgent:
    def __init__(self, verbose: bool = False, model: str = "gpt-4o-mini", provider: str = "openai"):
        """
        Initialize the dialogue splitter agent with necessary API keys.

        Args:
            verbose (bool): Whether to enable verbose output from CrewAI.
                Defaults to False.
            model (str): LLM model to use. Defaults to "gpt-4o-mini".
            provider (str): LLM provider to use. Defaults to "openai".
        """
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.verbose = verbose
        self.model = model
        self.provider = provider

        if not self.openai_api_key:
            raise ValueError("Missing required API key for DialogueSplitterAgent")

        # Initialize the LLM
        self.llm = ChatOpenAI(
            model=self.model,
            temperature=0.3,  # Lower temperature for more precise splitting
            api_key=self.openai_api_key
        )

    def split(self, story_data: Story) -> List[SceneSegment]:
        """
        Split the story into dialogue segments for narration and image generation.

        Args:
            story_data (Story): The story data with scenes

        Returns:
            List[SceneSegment]: List of scene segments with dialogue and timing information
        """
        logger.info("Starting dialogue splitting")

        # Create a parser for the list of SceneSegment models
        parser = SceneSegmentListParser.create()
        format_instructions = parser.get_format_instructions()

        # Create the splitter agent
        splitter = Agent(
            role="Dialogue Segmentation Specialist",
            goal="Split Hindi narration into optimal segments for TTS and image generation",
            backstory="""You are an expert in audio-visual storytelling. Your task is to
            split Hindi narration into optimal segments for text-to-speech conversion and
            image generation.""",
            verbose=self.verbose,
            allow_delegation=False,
            llm=self.llm
        )

        # Convert story data to string for the task
        story_str = json.dumps(story_data.model_dump(), ensure_ascii=False, indent=2)

        # Create the splitting task
        splitting_task = Task(
            description=f"""
            Split the following Hindi story into optimal segments for narration and image generation:

            {story_str}

            Your task is to:

            1. Analyze each scene's narration
            2. Split long narrations into smaller segments (15-30 seconds each)
            3. Ensure each segment has a clear visual cue that can be represented in an image
            4. Maintain the flow and coherence of the story

            IMPORTANT: The narration MUST be in Hindi (Devanagari script) - do not translate or modify the Hindi text content.
            Visual cues should be in English to guide image generation.
            """,
            agent=splitter,
            expected_output=format_instructions,
            llm=self.llm
        )

        # Create and run the crew
        crew = Crew(
            process=Process.sequential,
            tasks=[splitting_task],
            agents=[splitter],
            manager_llm=self.llm,
            verbose=self.verbose,
        )

        crew_output = crew.kickoff()
        result = crew_output.raw

        # Parse the result using the Pydantic parser
        segments = SceneSegmentListParser.parse_output(parser, result)

        # If parsing fails, log the error and exit
        if segments is None:
            logger.warning("Could not parse DialogueSplitter result, Raw output: %s", result)
            logger.warning("Please retry the task. Exiting...")
            exit(1)

        # Log detailed information about the segments
        logger.info(f"Dialogue splitting completed successfully, created {len(segments)} segments")
        for i, segment in enumerate(segments):
            logger.info(f"Segment {i+1}: Scene {segment.scene_number}, Segment {segment.segment_number}")

        return segments
