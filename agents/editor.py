"""
Editor Agent
-----------
Enhances the raw research report by filling gaps and adding missing details.
"""

import os
import json
import logging

from langchain_openai import ChatOpenAI
from crewai import Agent, Task, Crew, Process
from crewai_tools import SerperDevTool, ScrapeWebsiteTool

from utils.parsers import EnhancedResearchDataParser
from models.schema import ResearchData, EnhancedResearchData

logger = logging.getLogger(__name__)

class EditorAgent:
    def __init__(self, verbose: bool = False, model: str = "gpt-4o-mini", provider: str = "openai"):
        """
        Initialize the editor agent with necessary API keys and tools.

        Args:
            verbose (bool): Whether to enable verbose output from CrewAI.
                Defaults to False.
            model (str): LLM model to use. Defaults to "gpt-4o-mini".
            provider (str): LLM provider to use. Defaults to "openai".
        """
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.serper_api_key = os.getenv("SERPER_API_KEY")
        self.verbose = verbose
        self.model = model
        self.provider = provider

        if not self.openai_api_key or not self.serper_api_key:
            raise ValueError("Missing required API keys for EditorAgent")

        # Initialize tools
        self.serper_tool = SerperDevTool()
        self.scrape_website_tool = ScrapeWebsiteTool()
        self.tools = [self.serper_tool, self.scrape_website_tool]

        # Initialize the LLM
        self.llm = ChatOpenAI(
            model=self.model,
            temperature=0.7,
            api_key=self.openai_api_key
        )

    def enhance(self,
                research_data: ResearchData,
                story_type: str = 'real',
                genre: str = 'thriller') -> EnhancedResearchData:
        """
        Enhance the research data by filling gaps and adding missing details.

        Args:
            research_data (ResearchData): The raw research data
            story_type (str): Type of story - 'real', 'fictional', or 'mixed'
            genre (str): Genre of the story - thriller, romance, mystery, etc.

        Returns:
            EnhancedResearchData: Enhanced research data with additional narrative elements
        """
        logger.info(f"Starting enhancement of research data for {genre} {story_type} story")

        # Create a parser for the EnhancedResearchData model
        parser = EnhancedResearchDataParser.create()
        format_instructions = parser.get_format_instructions()

        # Create the editor agent with role based on story type and genre
        if story_type == 'real':
            editor_role = f"{genre.capitalize()} Factual Story Editor"
            editor_goal = f"Enhance and structure research data for a compelling Hindi {genre} story based on real incidents"
            editor_backstory = f"""You are an experienced editor specializing in factual {genre} storytelling.
            Your task is to enhance and structure the research data to make it
            suitable for creating a compelling Hindi {genre} story based on real events."""

        elif story_type == 'fictional':
            editor_role = f"{genre.capitalize()} Fiction Editor"
            editor_goal = f"Enhance and structure creative elements for a compelling fictional Hindi {genre} story"
            editor_backstory = f"""You are an experienced editor specializing in {genre} fiction.
            Your task is to enhance and structure the creative elements to make them
            suitable for creating a compelling fictional Hindi {genre} story."""

        else:  # story_type == 'mixed'
            editor_role = f"{genre.capitalize()} Hybrid Story Editor"
            editor_goal = f"Enhance and structure a blend of factual and fictional elements for a compelling Hindi {genre} story"
            editor_backstory = f"""You are an experienced editor specializing in blending fact and fiction in {genre} storytelling.
            Your task is to enhance and structure both the factual research and creative elements to make them
            suitable for creating a compelling Hindi {genre} story that combines real events with fictional elements."""

        editor = Agent(
            role=editor_role,
            goal=editor_goal,
            backstory=editor_backstory,
            verbose=self.verbose,
            allow_delegation=False,
            tools=self.tools,
            llm=self.llm
        )

        # Convert research data to string for the task
        research_str = json.dumps(research_data.model_dump(), ensure_ascii=False, indent=2)

        # Create the enhancement task with description based on story type and genre
        if story_type == 'real':
            task_description = f"""
            Enhance the following research data for a Hindi {genre} story based on real incidents:

            {research_str}

            Your task is to:

            1. Fill in any gaps in the research by using the search and web scraping tools to gather additional information
            2. Add missing details that would make the story more compelling as a {genre} story
            3. Ensure cultural authenticity for an Indian audience by researching relevant cultural elements
            4. Develop character sketches for potential protagonists and antagonists appropriate for a {genre} story
            5. Suggest atmospheric elements and settings that enhance the {genre} feel
            6. Enhance the narrative elements to make the story engaging while staying true to real events
            7. Incorporate typical {genre} tropes and conventions where appropriate
            8. Create a cohesive narrative arc with clear beginning, middle, and end
            9. Design seamless transitions between scenes to maintain listener engagement
            10. Develop engaging hooks at the beginning and throughout the story to maintain interest
            11. Ensure clear character motivations and development throughout the narrative
            """

        elif story_type == 'fictional':
            task_description = f"""
            Enhance the following creative elements for a fictional Hindi {genre} story:

            {research_str}

            Your task is to:

            1. Expand on the creative elements provided, using the search and web scraping tools to research similar stories and tropes
            2. Add rich details that would make the story more immersive as a {genre} story
            3. Ensure cultural authenticity for an Indian audience by researching relevant cultural elements
            4. Develop detailed character sketches for protagonists and antagonists with clear motivations and development arcs
            5. Create vivid atmospheric elements and settings that enhance the {genre} feel
            6. Enhance the narrative elements to make the story engaging and captivating
            7. Incorporate typical {genre} tropes and conventions where appropriate
            8. Create a cohesive narrative arc with clear beginning, middle, and end
            9. Design seamless transitions between scenes to maintain listener engagement
            10. Develop engaging hooks at the beginning and throughout the story to maintain interest
            11. Ensure proper pacing that builds tension and maintains listener attention
            """

        else:  # story_type == 'mixed'
            task_description = f"""
            Enhance the following blend of research and creative elements for a mixed Hindi {genre} story:

            {research_str}

            Your task is to:

            1. Balance the factual elements with creative fictional components, using the search and web scraping tools to gather additional information
            2. Add rich details that would make the story more immersive as a {genre} story
            3. Ensure cultural authenticity for an Indian audience by researching relevant cultural elements
            4. Develop detailed character sketches that blend real people with fictional elements, focusing on clear motivations and development
            5. Create vivid atmospheric elements and settings that enhance the {genre} feel
            6. Enhance the narrative elements to make the story engaging while respecting the real events
            7. Incorporate typical {genre} tropes and conventions where appropriate
            8. Create a cohesive narrative arc with clear beginning, middle, and end
            9. Design seamless transitions between scenes to maintain listener engagement
            10. Develop engaging hooks at the beginning and throughout the story to maintain interest
            11. Ensure proper pacing that builds tension and maintains listener attention
            12. Create natural-sounding dialogue and descriptions that feel authentic
            """

        # Create the enhancement task
        enhancement_task = Task(
            description=task_description,
            agent=editor,
            expected_output=format_instructions,
            llm=self.llm
        )

        # Create and run the crew
        crew = Crew(
            process=Process.sequential,
            tasks=[enhancement_task],
            agents=[editor],
            manager_llm=self.llm,
            verbose=self.verbose
        )

        crew_output = crew.kickoff()
        result = crew_output.raw

        # Parse the result using the Pydantic parser
        enhanced_data = EnhancedResearchDataParser.parse_output(parser, result)

        # If parsing fails, create a basic EnhancedResearchData from the original data
        if enhanced_data is None:
            logger.warning("Could not parse Editor result, Raw output: %s", result)
            logger.warning("Please retry the task. Exiting...")
            exit(1)

        logger.info("Research enhancement completed successfully")
        return enhanced_data
