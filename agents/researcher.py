"""
Researcher Agent
---------------
Unified agent that handles research for all story types:
- Real: Uses Serper API and web scraping to gather information related to real incidents
- Fictional: Creates fictional story elements without using web search tools
- Mixed: Combines real research with fictional elements
"""

import os
import logging

from langchain_openai import ChatOpenAI
from crewai import Agent, Task, Crew, Process
from crewai_tools import SerperDevTool, ScrapeWebsiteTool

from models.schema import ResearchData
from utils.parsers import ResearchDataParser

logger = logging.getLogger(__name__)

class ResearcherAgent:
    def __init__(self, verbose: bool = False, model: str = "gpt-4o-mini", provider: str = "openai"):
        """
        Initialize the researcher agent with necessary API keys and tools.

        Args:
            verbose (bool): Whether to enable verbose output from CrewAI.
                Defaults to False.
            model (str): LLM model to use. Defaults to "gpt-4o-mini".
            provider (str): LLM provider to use. Defaults to "openai".
        """
        self.serper_api_key = os.getenv("SERPER_API_KEY")
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.verbose = verbose
        self.model = model
        self.provider = provider

        if not self.serper_api_key or not self.openai_api_key:
            raise ValueError("Missing required API keys for ResearcherAgent")

        # Initialize the LLM
        self.llm = ChatOpenAI(
            model=self.model,
            temperature=0.7,
            api_key=self.openai_api_key
        )

    def research(self,
                 title: str,
                 context: str = "",
                 story_type: str = "real",
                 references: list = None) -> ResearchData:
        """
        Perform research based on the story type.

        Args:
            title (str): The title of the story
            context (str, optional): Additional context for the research
            story_type (str): Type of story - 'real', 'fictional', or 'mixed'
            references (list, optional): List of URLs to articles, blogs, or reports to analyze

        Returns:
            ResearchData: Research data in structured format using Pydantic model
        """
        logger.info(f"Starting {story_type} research for title: '{title}'")
        references = references or []

        # Create a parser for the ResearchData model
        parser = ResearchDataParser.create()
        format_instructions = parser.get_format_instructions()

        # Initialize tools for real and mixed story types
        tools = []

        if story_type in ['real', 'mixed']:
            serper_tool = SerperDevTool()
            scrape_website_tool = ScrapeWebsiteTool()
            tools = [serper_tool, scrape_website_tool]

        # Set agent parameters based on story type
        if story_type == 'real':
            role = "Research Specialist"
            goal = "Gather comprehensive information for a Hindi story based on real incidents"
            backstory = """You are an expert researcher specializing in factual storytelling.
            Your task is to gather information that can be used to create a
            compelling Hindi story based on real events or incidents."""
            temperature = 0.7

        elif story_type == 'fictional':
            role = "Creative Story Developer"
            goal = "Create compelling fictional elements for a Hindi story"
            backstory = """You are a creative Hindi storyteller with a rich imagination.
            Your task is to develop fictional story elements that can be used to create
            an engaging Hindi story based on the provided title and context."""
            temperature = 0.8  # Higher temperature for more creative outputs

        else:  # story_type == 'mixed'
            role = "Hybrid Story Researcher"
            goal = "Combine real research with creative elements for a compelling Hindi story"
            backstory = """You are a skilled researcher and storyteller who excels at blending
            fact with fiction. Your task is to research real events and enhance them with
            creative elements to create an engaging Hindi story."""
            temperature = 0.75

        # Adjust LLM temperature based on story type
        self.llm.temperature = temperature

        # Create the researcher agent
        researcher = Agent(
            role=role,
            goal=goal,
            backstory=backstory,
            verbose=self.verbose,
            allow_delegation=False,
            tools=tools,
            llm=self.llm
        )

        # Create the research task based on story type
        if story_type == 'real':
            task_description = f"""
            Research the topic: "{title}" {f"with context: {context}" if context else ""}

            {f"Analyze these specific references: {', '.join(references)}" if references else ""}

            1. Search for relevant information, news, events, or real incidents related to the topic
            2. Focus on content that would be relevant for an Indian audience
            3. Gather details that can be used to create a compelling factual story
            4. Organize your findings into a structured report
            """

        elif story_type == 'fictional':
            task_description = f"""
            Create a fictional story framework based on:

            Title: "{title}"
            Context: "{context}"

            Your task is to:

            1. Develop a fictional story world based on the title and context
            2. Create interesting characters with clear motivations
            3. Design a compelling narrative arc with conflict and resolution
            4. Include cultural elements relevant to an Indian audience
            5. Organize your ideas into a structured report
            """

        else:  # story_type == 'mixed'
            task_description = f"""
            Research and create a mixed story framework based on:

            Title: "{title}"
            Context: "{context}"
            {f"References: {', '.join(references)}" if references else ""}

            Your task is to:

            1. Research real events, incidents, or facts related to the topic
            2. Blend these real elements with creative fictional components
            3. Create interesting characters based on or inspired by real people
            4. Design a compelling narrative that respects the real events while enhancing them
            5. Include cultural elements relevant to an Indian audience
            6. Organize your findings and ideas into a structured report
            """

        # Create the research task
        research_task = Task(
            description=task_description,
            agent=researcher,
            expected_output=format_instructions,
            llm=self.llm
        )

        # Create and run the crew
        crew = Crew(
            process=Process.sequential,
            tasks=[research_task],
            agents=[researcher],
            manager_llm=self.llm,
            verbose=self.verbose,
        )

        crew_output = crew.kickoff()
        result = crew_output.raw

        # Parse the result using the Pydantic parser
        research_data = ResearchDataParser.parse_output(parser, result)

        # If parsing fails, log the error and exit
        if research_data is None:
            logger.warning("Could not parse Research result, Raw output: %s", result)
            logger.warning("Please retry the task. Exiting...")
            exit(1)

        logger.info("Research completed successfully")
        return research_data
