"""
Story Editor Agent
-----------------
Interactive agent that edits and refines story scenes based on user feedback.
"""

import os
import re
import json
import logging

from langchain_openai import ChatOpenAI
from crewai import Agent, Task, Crew, Process
from crewai_tools import SerperDevTool, ScrapeWebsiteTool

from models.schema import Story, Scene
from utils.parsers import StoryParser

logger = logging.getLogger(__name__)


class StoryEditorAgent:
    def __init__(self, verbose: bool = False, model: str = "gpt-4o-mini", provider: str = "openai"):
        """
        Initialize the story editor agent with necessary API keys.

        Args:
            verbose (bool): Whether to enable verbose output from CrewAI.
                Defaults to False.
            model (str): LLM model to use. Defaults to "gpt-4o-mini".
            provider (str): LLM provider to use. Defaults to "openai".
        """
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.verbose = verbose
        self.model = model
        self.provider = provider

        if not self.openai_api_key:
            raise ValueError("Missing required API key for StoryEditorAgent")

        # Initialize the LLM with higher temperature for creative editing
        self.llm = ChatOpenAI(
            model=self.model,
            temperature=0.8,
            api_key=self.openai_api_key
        )

    def display_story(self, story: Story) -> None:
        """
        Display the entire story in the terminal for review.

        Args:
            story (Story): The story to display
        """
        print("\n" + "="*80)
        print(f"STORY: {story.title}")
        print("="*80)

        for scene in story.scenes:
            print(f"\nSCENE {scene.scene_number}:")
            print("-"*80)
            if scene.narrative_purpose:
                print(f"PURPOSE: {scene.narrative_purpose}")
            if scene.transition_from_previous:
                print(f"TRANSITION: {scene.transition_from_previous}")
            print(f"NARRATION: {scene.narration}")
            print(f"VISUAL: {scene.visual_description}")
            print("-"*80)

    def edit_scene(self, story: Story, scene_number: int, feedback: str) -> Scene:
        """
        Edit a specific scene based on user feedback.

        Args:
            story (Story): The full story context
            scene_number (int): The scene number to edit
            feedback (str): User feedback on what needs to be improved

        Returns:
            Scene: The edited scene
        """
        logger.info(f"Editing scene {scene_number} based on user feedback")

        # Create a parser for the Scene model
        parser = StoryParser.create_scene_parser()
        format_instructions = parser.get_format_instructions()

        # Initialize search tools
        serper_api_key = os.getenv("SERPER_API_KEY")

        if not serper_api_key:
            logger.warning("SERPER_API_KEY not found. Search functionality will be limited.")
            search_tools = []

        else:
            serper_tool = SerperDevTool()
            scrape_website_tool = ScrapeWebsiteTool()
            search_tools = [serper_tool, scrape_website_tool]
            logger.info("Search tools initialized for scene editing")

        # Create the editor agent
        editor = Agent(
            role="Hindi Story Editor",
            goal="Enhance the story to match documentary-style Hindi narration inspired by Nitish Rajput, focusing on emotional intensity, rhetorical flow, and powerful takeaway lines",
            backstory="""You are an expert Hindi story editor with a deep understanding of narrative structure,
            pacing, emotional impact, and cultural authenticity. Your task is to refine and improve
            story scenes based on specific feedback while maintaining the overall story context.
            Your editing should align with the narration style of Hindi documentary creators like Nitish Rajput — reflective, immersive, and fact-based storytelling delivered like a personal monologue to the audience. Blend drama with realism. Use rhetorical questions, callbacks, emotional hooks, and dramatic pauses where appropriate.
            You can use search tools to research relevant information when needed, especially for stories
            based on real incidents or when specific factual details would enhance the scene.""",
            verbose=self.verbose,
            allow_delegation=False,
            tools=search_tools,
            llm=self.llm
        )

        # Get the scene to edit
        scene_to_edit = next((s for s in story.scenes if s.scene_number == scene_number), None)
        if not scene_to_edit:
            logger.error(f"Scene {scene_number} not found in the story")
            return None

        # Get previous and next scenes for context (if they exist)
        prev_scene = next((s for s in story.scenes if s.scene_number == scene_number - 1), None)
        next_scene = next((s for s in story.scenes if s.scene_number == scene_number + 1), None)

        # Convert scenes to string for the task
        scene_str = json.dumps(scene_to_edit.model_dump(), ensure_ascii=False, indent=2)
        prev_scene_str = json.dumps(prev_scene.model_dump(), ensure_ascii=False, indent=2) if prev_scene else "None"
        next_scene_str = json.dumps(next_scene.model_dump(), ensure_ascii=False, indent=2) if next_scene else "None"

        # Create the editing task
        editing_task = Task(
            description=f"""
            Edit and improve the following scene from a Hindi story based on specific feedback:

            STORY TITLE: {story.title}

            SCENE TO EDIT:
            {scene_str}

            PREVIOUS SCENE (for context):
            {prev_scene_str}

            NEXT SCENE (for context):
            {next_scene_str}

            USER FEEDBACK:
            {feedback}

            Your task is to:

            1. Carefully analyze the user's feedback and understand what needs to be improved
            2. Edit the scene's narration to address the feedback while maintaining the story's context
            3. Improve the narrative quality, emotional impact, and flow of the scene
            4. Ensure smooth transitions from the previous scene and to the next scene
            5. Maintain cultural authenticity and natural Hindi language
            6. Preserve the core story elements while enhancing the narrative
            7. Update the visual description to match any changes in the narration
            8. Fill in the transition_from_previous field with a clear description of how this scene connects to the previous one
            9. Fill in the narrative_purpose field to clarify this scene's role in the overall story
            10. Use the search tools (SerperDevTool and ScrapeWebsiteTool) when needed to:
               - Research factual information for stories based on real incidents
               - Find cultural or historical details that would enhance authenticity
               - Look up specific terminology or concepts mentioned in the user feedback
               - Gather information about locations, events, or people relevant to the scene

            IMPORTANT GUIDELINES:
            - ALL narration text MUST be in Hindi using Devanagari script - this is the content that will be spoken in the final video
            - Do NOT translate or modify the Hindi text content unless specifically requested in the feedback
            - Visual descriptions should be in English
            - Transition and narrative purpose should be in English
            - Maintain the scene_number as is
            - Focus on creating a compelling, reflective, and emotionally immersive narration — as if one person is passionately recounting a true story to a wide audience
            - Ensure the scene flows naturally from the previous scene and into the next scene
            - Pay special attention to hooks, pacing, character development, and dialogue
            - When using search tools, incorporate the information naturally into the scene
            """,
            agent=editor,
            expected_output=format_instructions,
            llm=self.llm
        )

        # Create and run the crew
        crew = Crew(
            process=Process.sequential,
            tasks=[editing_task],
            agents=[editor],
            manager_llm=self.llm,
            verbose=self.verbose,
        )

        crew_output = crew.kickoff()
        result = crew_output.raw

        # Parse the result using the Pydantic parser
        edited_scene = StoryParser.parse_scene_output(parser, result)

        # If parsing fails, return the original scene
        if edited_scene is None:
            logger.warning(f"Could not parse Editor result for scene {scene_number}, Raw output: {result}")
            return scene_to_edit

        logger.info(f"Scene {scene_number} edited successfully")
        return edited_scene

    def update_story(self, story: Story, edited_scene: Scene) -> Story:
        """
        Update the story with an edited scene.

        Args:
            story (Story): The original story
            edited_scene (Scene): The edited scene

        Returns:
            Story: The updated story
        """
        # Create a new list of scenes with the edited scene
        updated_scenes = [
            edited_scene if s.scene_number == edited_scene.scene_number else s
            for s in story.scenes
        ]

        # Create a new Story object with the updated scenes
        updated_story = Story(
            title=story.title,
            scenes=updated_scenes
        )

        return updated_story

    def remove_scene(self, story: Story, scene_number: int) -> Story:
        """
        Remove a scene from the story and adjust all subsequent scene numbers.

        Args:
            story (Story): The original story
            scene_number (int): The scene number to remove

        Returns:
            Story: The updated story with the scene removed and renumbered scenes
        """
        # Check if the scene number is valid
        if scene_number < 1 or scene_number > len(story.scenes):
            logger.error(f"Invalid scene number {scene_number} for removal")
            return story

        # Filter out the scene to remove
        remaining_scenes = [s for s in story.scenes if s.scene_number != scene_number]

        # Adjust scene numbers for all scenes after the removed one
        for i, scene in enumerate(remaining_scenes):
            if scene.scene_number > scene_number:
                # Create a new scene with updated scene number
                updated_scene = Scene(
                    scene_number=scene.scene_number - 1,
                    narration=scene.narration,
                    visual_description=scene.visual_description,
                    transition_from_previous=scene.transition_from_previous,
                    narrative_purpose=scene.narrative_purpose
                )
                remaining_scenes[i] = updated_scene

        # Sort scenes by scene number to ensure proper order
        remaining_scenes.sort(key=lambda s: s.scene_number)

        # Create a new Story object with the updated scenes
        updated_story = Story(
            title=story.title,
            scenes=remaining_scenes
        )

        return updated_story

    def move_scene(self, story: Story, from_position: int, to_position: int) -> Story:
        """
        Move a scene from one position to another and adjust all affected scene numbers.

        Args:
            story (Story): The original story
            from_position (int): The current scene number to move
            to_position (int): The target position for the scene

        Returns:
            Story: The updated story with the scene moved and renumbered scenes
        """
        # Check if the positions are valid
        if from_position < 1 or from_position > len(story.scenes) or to_position < 1 or to_position > len(story.scenes):
            logger.error(f"Invalid scene positions: from {from_position} to {to_position}")
            return story

        # If the positions are the same, no change is needed
        if from_position == to_position:
            return story

        # Extract the scene to move
        scene_to_move = next((s for s in story.scenes if s.scene_number == from_position), None)
        if not scene_to_move:
            logger.error(f"Scene {from_position} not found")
            return story

        # Create a copy of all scenes
        updated_scenes = []

        # Handle scene renumbering based on whether we're moving up or down
        if from_position < to_position:

            # Moving a scene to a later position
            for scene in story.scenes:

                if scene.scene_number == from_position:
                    # Skip the scene being moved (we'll add it later)
                    continue

                elif from_position < scene.scene_number <= to_position:
                    # Shift scenes in between down by one
                    updated_scenes.append(Scene(
                        scene_number=scene.scene_number - 1,
                        narration=scene.narration,
                        visual_description=scene.visual_description,
                        transition_from_previous=scene.transition_from_previous,
                        narrative_purpose=scene.narrative_purpose
                    ))

                else:
                    # Keep other scenes as they are
                    updated_scenes.append(scene)

        else:

            # Moving a scene to an earlier position
            for scene in story.scenes:

                if scene.scene_number == from_position:
                    # Skip the scene being moved (we'll add it later)
                    continue

                elif to_position <= scene.scene_number < from_position:
                    # Shift scenes in between up by one
                    updated_scenes.append(Scene(
                        scene_number=scene.scene_number + 1,
                        narration=scene.narration,
                        visual_description=scene.visual_description,
                        transition_from_previous=scene.transition_from_previous,
                        narrative_purpose=scene.narrative_purpose
                    ))

                else:
                    # Keep other scenes as they are
                    updated_scenes.append(scene)

        # Add the moved scene at its new position
        moved_scene = Scene(
            scene_number=to_position,
            narration=scene_to_move.narration,
            visual_description=scene_to_move.visual_description,
            transition_from_previous=scene_to_move.transition_from_previous,
            narrative_purpose=scene_to_move.narrative_purpose
        )
        updated_scenes.append(moved_scene)

        # Sort scenes by scene number to ensure proper order
        updated_scenes.sort(key=lambda s: s.scene_number)

        # Create a new Story object with the updated scenes
        updated_story = Story(
            title=story.title,
            scenes=updated_scenes
        )

        return updated_story

    def add_new_scene(self, story: Story, position: int, instructions: str) -> Story:
        """
        Add a new scene at the specified position and adjust all subsequent scene numbers.

        Args:
            story (Story): The original story
            position (int): The position where the new scene should be inserted
                           (1 = beginning of story, len(story.scenes) + 1 = end of story)
            instructions (str): User instructions for generating the new scene

        Returns:
            Story: The updated story with the new scene inserted and renumbered scenes
        """
        # Check if the position is valid
        if position < 1 or position > len(story.scenes) + 1:
            logger.error(f"Invalid position {position} for new scene")
            return story

        # Get context from surrounding scenes for better scene generation
        prev_scene = None
        next_scene = None

        if position > 1:
            prev_scene = next((s for s in story.scenes if s.scene_number == position - 1), None)

        if position <= len(story.scenes):
            next_scene = next((s for s in story.scenes if s.scene_number == position), None)

        # Create a parser for the Scene model
        parser = StoryParser.create_scene_parser()
        format_instructions = parser.get_format_instructions()

        # Initialize search tools
        serper_api_key = os.getenv("SERPER_API_KEY")

        if not serper_api_key:
            logger.warning("SERPER_API_KEY not found. Search functionality will be limited.")
            search_tools = []

        else:
            serper_tool = SerperDevTool()
            scrape_website_tool = ScrapeWebsiteTool()
            search_tools = [serper_tool, scrape_website_tool]
            logger.info("Search tools initialized for scene generation")

        # Create the scene generator agent
        scene_generator = Agent(
            role="Hindi Story Scene Generator",
            goal="Generate a new scene that fits into a Hindi documentary-style narrative delivered like a real story, with dramatic realism and emotional insight",
            backstory="""You are an expert Hindi story writer with a deep understanding of narrative structure,
            pacing, emotional impact, and cultural authenticity. Your task is to create a new scene
            that fits naturally into an existing story based on the surrounding context and user instructions.
            The new scene should reflect the style of Hindi factual storytelling popularized by creators like Nitish Rajput — grounded in realism, emotionally compelling, and delivered in a monologue format. Use suspenseful buildup, emotional questions, and morally reflective conclusions where needed.
            You can use search tools to research relevant information when needed, especially for stories
            based on real incidents or when specific factual details would enhance the scene.""",
            verbose=self.verbose,
            allow_delegation=False,
            tools=search_tools,
            llm=self.llm
        )

        # Convert context scenes to string for the task
        prev_scene_str = json.dumps(prev_scene.model_dump(), ensure_ascii=False, indent=2) if prev_scene else "None"
        next_scene_str = json.dumps(next_scene.model_dump(), ensure_ascii=False, indent=2) if next_scene else "None"
        story_title = story.title

        # Create the scene generation task
        generation_task = Task(
            description=f"""
            Generate a new scene to be inserted into a Hindi story based on the following context and instructions:

            STORY TITLE: {story_title}

            PREVIOUS SCENE (for context):
            {prev_scene_str}

            NEXT SCENE (for context):
            {next_scene_str}

            POSITION: This scene will be inserted at position {position} in the story.

            USER INSTRUCTIONS:
            {instructions}

            Your task is to:

            1. Create a new scene that fits seamlessly between the previous and next scenes
            2. Ensure the scene aligns with the overall narrative flow and story context
            3. Write the narration in Hindi (Devanagari script)
            4. Create a vivid visual description in English
            5. Establish a clear transition from the previous scene (if any)
            6. Define the narrative purpose of this scene in the overall story
            7. Ensure the scene advances the plot or develops characters in meaningful ways
            8. Maintain the tone, style, and themes of the surrounding story
            9. Use the search tools (SerperDevTool and ScrapeWebsiteTool) when needed to:
               - Research factual information for stories based on real incidents
               - Find cultural or historical details that would enhance authenticity
               - Look up specific terminology or concepts mentioned in the user instructions
               - Gather information about locations, events, or people relevant to the scene

            IMPORTANT GUIDELINES:
            - ALL narration text MUST be in Hindi using Devanagari script - this is the content that will be spoken in the final video
            - Visual descriptions should be in English
            - Transition and narrative purpose should be in English
            - The scene_number should be {position}
            - Focus on creating engaging, emotionally impactful narrative
            - Ensure the scene flows naturally from the previous scene and into the next scene
            - Pay special attention to hooks, pacing, character development, and dialogue
            - When using search tools, incorporate the information naturally into the scene
            """,
            agent=scene_generator,
            expected_output=format_instructions,
            llm=self.llm
        )

        # Create and run the crew
        crew = Crew(
            process=Process.sequential,
            tasks=[generation_task],
            agents=[scene_generator],
            manager_llm=self.llm,
            verbose=self.verbose,
        )

        crew_output = crew.kickoff()
        result = crew_output.raw

        # Parse the result using the Pydantic parser
        new_scene = StoryParser.parse_scene_output(parser, result)

        # If parsing fails, create a basic scene
        if new_scene is None:
            logger.warning(f"Could not parse Scene Generator result, Raw output: {result}")
            new_scene = Scene(
                scene_number=position,
                narration="[Scene generation failed. Please try again with different instructions.]",
                visual_description="[Scene generation failed]",
                transition_from_previous="",
                narrative_purpose="[Failed to generate]"
            )

        # Create a copy of all scenes with adjusted scene numbers
        updated_scenes = []

        # Add existing scenes with adjusted scene numbers
        for scene in story.scenes:

            if scene.scene_number < position:
                # Keep scenes before the insertion point as they are
                updated_scenes.append(scene)

            else:
                # Increment scene numbers for scenes after the insertion point
                updated_scenes.append(Scene(
                    scene_number=scene.scene_number + 1,
                    narration=scene.narration,
                    visual_description=scene.visual_description,
                    transition_from_previous=scene.transition_from_previous,
                    narrative_purpose=scene.narrative_purpose
                ))

        # Add the new scene
        updated_scenes.append(new_scene)

        # Sort scenes by scene number to ensure proper order
        updated_scenes.sort(key=lambda s: s.scene_number)

        # Create a new Story object with the updated scenes
        updated_story = Story(
            title=story.title,
            scenes=updated_scenes
        )

        logger.info(f"New scene added at position {position}")
        return updated_story

    def save_story_to_json(self, story: Story, story_json_path: str) -> None:
        """
        Save the story to a JSON file.

        Args:
            story (Story): The story to save
            story_json_path (str): Path to the JSON file
        """
        try:
            with open(story_json_path, 'w', encoding='utf-8') as f:
                json.dump(story.model_dump(), f, ensure_ascii=False, indent=2)

            logger.info(f"Story saved to {story_json_path}")

        except Exception as e:
            logger.error(f"Error saving story to {story_json_path}: {str(e)}")
            print(f"\nError saving story: {str(e)}")

    def add_or_update_multiple_scenes(self, story: Story, context_prompt: str) -> Story:
        """
        Intelligently add or update multiple scenes based on user-provided context or prompt.

        This method analyzes the story structure, determines where new scenes should be inserted
        or which existing scenes need modification, and makes the necessary changes to incorporate
        the new context while maintaining narrative coherence.

        Args:
            story (Story): The original story
            context_prompt (str): User-provided context or prompt for scene generation/modification

        Returns:
            Story: The updated story with new or modified scenes
        """
        logger.info(f"Analyzing story to add or update scenes based on context: {context_prompt[:50]}...")

        # We don't need a parser here as we'll be parsing the JSON directly

        # Initialize search tools
        serper_api_key = os.getenv("SERPER_API_KEY")

        if not serper_api_key:
            logger.warning("SERPER_API_KEY not found. Search functionality will be limited.")
            search_tools = []

        else:
            serper_tool = SerperDevTool()
            scrape_website_tool = ScrapeWebsiteTool()
            search_tools = [serper_tool, scrape_website_tool]
            logger.info("Search tools initialized for multi-scene generation")

        # Create the multi-scene generator agent
        multi_scene_generator = Agent(
            role="Hindi Story Multi-Scene Generator",
            goal="Analyze a story and intelligently add or update multiple scenes based on user context while maintaining narrative coherence",
            backstory="""You are an expert Hindi story writer and editor with a deep understanding of narrative structure,
            pacing, emotional impact, and cultural authenticity. Your task is to analyze an existing story,
            determine where new scenes should be added or which existing scenes need modification, and make
            the necessary changes to incorporate new context while maintaining narrative coherence.

            You excel at understanding story structure and can identify the most appropriate places to add new content
            or enhance existing scenes. You can determine if a user's request requires adding completely new scenes,
            modifying existing ones, or a combination of both. You maintain the style of Hindi factual storytelling
            popularized by creators like Nitish Rajput — grounded in realism, emotionally compelling, and delivered
            in a monologue format.

            You can use search tools to research relevant information when needed, especially for stories
            based on real incidents or when specific factual details would enhance the scenes.""",
            verbose=self.verbose,
            allow_delegation=False,
            tools=search_tools,
            llm=self.llm
        )

        # Convert the entire story to string for the task
        story_str = json.dumps(story.model_dump(), ensure_ascii=False, indent=2)

        # Create the multi-scene generation task
        generation_task = Task(
            description=f"""
            Analyze the following Hindi story and determine how to best incorporate the user-provided context
            by adding new scenes and/or updating existing scenes:

            STORY TITLE: {story.title}

            FULL STORY STRUCTURE:
            {story_str}

            USER-PROVIDED CONTEXT/PROMPT:
            {context_prompt}

            Your task is to:

            1. Carefully analyze the existing story structure and content
            2. Analyze the user-provided context/prompt to understand what needs to be added or modified
            3. Research any necessary information using the search tools if needed
            4. Determine the most appropriate approach:
               - Identify where new scenes should be inserted
               - Identify which existing scenes should be modified
               - Determine if a combination of new and modified scenes is needed
            5. For each scene you decide to add or modify, provide:
               - Scene number (for new scenes, indicate where they should be inserted)
               - Whether it's a new scene or a modification of an existing scene
               - Complete scene content including:
                 * Hindi narration (in Devanagari script)
                 * Visual description (in English)
                 * Transition from previous scene (in English)
                 * Narrative purpose (in English)
            6. Ensure all changes maintain narrative coherence and flow with the existing story
            7. Ensure proper scene numbering and references throughout the story
            8. Provide a brief explanation of your changes and how they enhance the story

            IMPORTANT GUIDELINES:
            - ALL narration text MUST be in Hindi using Devanagari script - this is the content that will be spoken in the final video
            - Visual descriptions, transitions, and narrative purposes should be in English
            - Focus on creating compelling, reflective, and emotionally immersive narration
            - Ensure all scenes flow naturally and maintain the overall narrative structure
            - Pay special attention to hooks, pacing, character development, and dialogue
            - When using search tools, incorporate the information naturally into the scenes
            - Your output should be a JSON array of scene objects, each containing:
              * scene_number: The scene number (integer)
              * is_new: Whether this is a new scene (boolean)
              * narration: The Hindi narration text (string)
              * visual_description: Description of what should be shown visually (string)
              * transition_from_previous: How this scene connects to the previous one (string)
              * narrative_purpose: This scene's role in the overall story (string)
            """,
            agent=multi_scene_generator,
            expected_output="""
            Your output should be a JSON array of scene objects, each containing:
            - scene_number: The scene number (integer)
            - is_new: Whether this is a new scene (boolean)
            - narration: The Hindi narration text (string)
            - visual_description: Description of what should be shown visually (string)
            - transition_from_previous: How this scene connects to the previous one (string)
            - narrative_purpose: This scene's role in the overall story (string)

            Example:
            [
              {
                "scene_number": 2,
                "is_new": true,
                "narration": "हिंदी में कहानी का नैरेशन...",
                "visual_description": "Visual description in English...",
                "transition_from_previous": "Transition from previous scene...",
                "narrative_purpose": "Purpose of this scene in the story..."
              },
              {
                "scene_number": 4,
                "is_new": false,
                "narration": "मौजूदा सीन का अपडेटेड नैरेशन...",
                "visual_description": "Updated visual description...",
                "transition_from_previous": "Updated transition...",
                "narrative_purpose": "Updated purpose..."
              }
            ]

            Also include a brief explanation of your changes and how they enhance the story.
            """,
            llm=self.llm
        )

        # Create and run the crew
        crew = Crew(
            process=Process.sequential,
            tasks=[generation_task],
            agents=[multi_scene_generator],
            manager_llm=self.llm,
            verbose=self.verbose,
        )

        print("\nAnalyzing story and generating new/updated scenes based on your context. This may take a moment...")
        crew_output = crew.kickoff()
        result = crew_output.raw

        # Extract the JSON array from the result
        try:

            # Try to find a JSON array in the output
            json_match = re.search(r'(\[[\s\S]*\])', result)

            if json_match:
                json_str = json_match.group(1)
                scene_updates = json.loads(json_str)

                # Extract explanation if available (text after the JSON array)
                explanation = result.split(json_str)[-1].strip()
                if explanation:
                    print("\nEXPLANATION OF CHANGES:")
                    print(explanation)

            else:
                logger.warning("Could not find JSON array in the output")
                print("\nFailed to parse the generated scenes. Please try again with a different context.")
                return story

        except Exception as e:
            logger.warning(f"Error parsing multi-scene generator output: {str(e)}")
            print(f"\nError processing scene updates: {str(e)}")
            return story

        # Process each scene update
        updated_story = story
        scenes_added = []
        scenes_modified = []

        # First pass: handle modifications to existing scenes
        for scene_update in [s for s in scene_updates if not s.get("is_new", True)]:
            scene_num = scene_update.get("scene_number")

            # Create a Scene object from the update
            updated_scene = Scene(
                scene_number=scene_num,
                narration=scene_update.get("narration", ""),
                visual_description=scene_update.get("visual_description", ""),
                transition_from_previous=scene_update.get("transition_from_previous", ""),
                narrative_purpose=scene_update.get("narrative_purpose", "")
            )

            # Update the scene in the story
            updated_story = self.update_story(updated_story, updated_scene)
            scenes_modified.append(scene_num)

            logger.info(f"Modified existing scene {scene_num}")

        # Second pass: handle new scenes (in ascending order of scene number)
        new_scene_updates = sorted([s for s in scene_updates if s.get("is_new", True)],
                                    key=lambda s: s.get("scene_number", 0))

        for scene_update in new_scene_updates:
            position = scene_update.get("scene_number")

            # Create a Scene object from the update
            new_scene = Scene(
                scene_number=position,
                narration=scene_update.get("narration", ""),
                visual_description=scene_update.get("visual_description", ""),
                transition_from_previous=scene_update.get("transition_from_previous", ""),
                narrative_purpose=scene_update.get("narrative_purpose", "")
            )

            # Create a copy of all scenes with adjusted scene numbers
            updated_scenes = []

            # Add existing scenes with adjusted scene numbers
            for scene in updated_story.scenes:
                if scene.scene_number < position:
                    # Keep scenes before the insertion point as they are
                    updated_scenes.append(scene)
                else:
                    # Increment scene numbers for scenes after the insertion point
                    updated_scenes.append(Scene(
                        scene_number=scene.scene_number + 1,
                        narration=scene.narration,
                        visual_description=scene.visual_description,
                        transition_from_previous=scene.transition_from_previous,
                        narrative_purpose=scene.narrative_purpose
                    ))

            # Add the new scene
            updated_scenes.append(new_scene)

            # Sort scenes by scene number to ensure proper order
            updated_scenes.sort(key=lambda s: s.scene_number)

            # Create a new Story object with the updated scenes
            updated_story = Story(
                title=updated_story.title,
                scenes=updated_scenes
            )

            scenes_added.append(position)
            logger.info(f"Added new scene at position {position}")

        # Print summary of changes
        if scenes_modified:
            print(f"\nModified {len(scenes_modified)} existing scenes: {', '.join(map(str, scenes_modified))}")

        if scenes_added:
            print(f"Added {len(scenes_added)} new scenes at positions: {', '.join(map(str, scenes_added))}")

        if not scenes_modified and not scenes_added:
            print("\nNo changes were made to the story. The agent may have failed to generate valid scene updates.")

        return updated_story

    def interactive_edit(self, story: Story, story_json_path: str = None) -> Story:
        """
        Run an interactive editing session for the story.

        Args:
            story (Story): The story to edit
            story_json_path (str, optional): Path to save the story JSON file after each edit.
                                            If None, the story will not be saved during editing.

        Returns:
            Story: The edited story
        """
        edited_story = story

        while True:
            # Display the current state of the story
            self.display_story(edited_story)

            # Ask the user what they want to do
            print("\nEDITING OPTIONS:")
            print("1. Edit a specific scene")
            print("2. Remove a scene")
            print("3. Move a scene to a different position")
            print("4. Add a new scene")
            print("5. Add or update multiple scenes based on context")
            print("6. Finish editing and continue")

            choice = input("\nEnter your choice (1-6): ").strip()

            if choice == "6":
                break

            elif choice == "5":
                # Get context for multiple scene generation/modification
                context_prompt = input("\nProvide context or prompt for adding/updating multiple scenes: ").strip()
                if not context_prompt:
                    print("Context cannot be empty. Please try again.")
                    continue

                # Add or update multiple scenes
                edited_story = self.add_or_update_multiple_scenes(edited_story, context_prompt)

                # Save the story to JSON after the updates
                if story_json_path:
                    self.save_story_to_json(edited_story, story_json_path)
                    print(f"Story updated in {story_json_path}")

            elif choice == "1":
                # Ask which scene to edit
                scene_num_input = input("\nEnter the scene number to edit: ").strip()

                try:
                    scene_num = int(scene_num_input)

                    if scene_num < 1 or scene_num > len(edited_story.scenes):
                        print(f"Invalid scene number. Please enter a number between 1 and {len(edited_story.scenes)}.")
                        continue

                except ValueError:
                    print("Please enter a valid number.")
                    continue

                # Get feedback for the scene
                feedback = input("\nEnter your feedback for this scene (what needs to be improved): ").strip()
                if not feedback:
                    print("Feedback cannot be empty. Please try again.")
                    continue

                # Edit the scene
                edited_scene = self.edit_scene(edited_story, scene_num, feedback)

                if edited_scene:

                    # Update the story with the edited scene
                    edited_story = self.update_story(edited_story, edited_scene)
                    print(f"\nScene {scene_num} has been updated.")

                    # Save the story to JSON after the edit
                    if story_json_path:
                        self.save_story_to_json(edited_story, story_json_path)
                        print(f"Story updated in {story_json_path}")

                else:
                    print(f"\nFailed to edit scene {scene_num}. Please try again.")

            elif choice == "2":
                # Ask which scene to remove
                scene_num_input = input("\nEnter the scene number to remove: ").strip()

                try:
                    scene_num = int(scene_num_input)

                    if scene_num < 1 or scene_num > len(edited_story.scenes):
                        print(f"Invalid scene number. Please enter a number between 1 and {len(edited_story.scenes)}.")
                        continue

                except ValueError:
                    print("Please enter a valid number.")
                    continue

                # Confirm removal
                confirm = input(f"Are you sure you want to remove scene {scene_num}? This cannot be undone. (y/n): ").strip().lower()
                if confirm != 'y':
                    print("Scene removal cancelled.")
                    continue

                # Remove the scene
                edited_story = self.remove_scene(edited_story, scene_num)
                print(f"\nScene {scene_num} has been removed and all subsequent scenes have been renumbered.")

                # Save the story to JSON after the removal
                if story_json_path:
                    self.save_story_to_json(edited_story, story_json_path)
                    print(f"Story updated in {story_json_path}")

            elif choice == "3":
                # Ask which scene to move
                from_input = input("\nEnter the scene number to move: ").strip()

                try:
                    from_pos = int(from_input)

                    if from_pos < 1 or from_pos > len(edited_story.scenes):
                        print(f"Invalid scene number. Please enter a number between 1 and {len(edited_story.scenes)}.")
                        continue

                except ValueError:
                    print("Please enter a valid number.")
                    continue

                # Ask where to move it
                to_input = input(f"Enter the new position for scene {from_pos} (1-{len(edited_story.scenes)}): ").strip()

                try:
                    to_pos = int(to_input)

                    if to_pos < 1 or to_pos > len(edited_story.scenes):
                        print(f"Invalid position. Please enter a number between 1 and {len(edited_story.scenes)}.")
                        continue

                except ValueError:
                    print("Please enter a valid number.")
                    continue

                # Move the scene
                edited_story = self.move_scene(edited_story, from_pos, to_pos)
                print(f"\nScene {from_pos} has been moved to position {to_pos} and all affected scenes have been renumbered.")

                # Save the story to JSON after the move
                if story_json_path:
                    self.save_story_to_json(edited_story, story_json_path)
                    print(f"Story updated in {story_json_path}")

            elif choice == "4":
                # Ask where to insert the new scene
                position_input = input(f"\nEnter the position where you want to add the new scene (1-{len(edited_story.scenes) + 1}): ").strip()

                try:
                    position = int(position_input)

                    if position < 1 or position > len(edited_story.scenes) + 1:
                        print(f"Invalid position. Please enter a number between 1 and {len(edited_story.scenes) + 1}.")
                        continue

                except ValueError:
                    print("Please enter a valid number.")
                    continue

                # Get instructions for the new scene
                instructions = input("\nProvide instructions or context for the new scene: ").strip()

                if not instructions:
                    print("Instructions cannot be empty. Please try again.")
                    continue

                print("\nGenerating new scene based on your instructions. This may take a moment...")

                # Add the new scene
                edited_story = self.add_new_scene(edited_story, position, instructions)
                print(f"\nNew scene has been added at position {position} and all subsequent scenes have been renumbered.")

                # Save the story to JSON after adding the new scene
                if story_json_path:
                    self.save_story_to_json(edited_story, story_json_path)
                    print(f"Story updated in {story_json_path}")

            else:
                print("Invalid choice. Please enter a number between 1 and 6.")

        return edited_story
