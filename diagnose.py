#!/usr/bin/env python3
"""
Diagnostic script to check the video generation process.
"""

import os
import json
import logging
import argparse
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_latest_story_dir():
    """Get the most recently created story directory."""
    assets_dir = 'assets'
    if not os.path.exists(assets_dir):
        return None

    # Get all subdirectories in the assets directory
    subdirs = [d for d in os.listdir(assets_dir) if os.path.isdir(os.path.join(assets_dir, d))]
    if not subdirs:
        return None

    # Sort by creation time (newest first)
    subdirs.sort(key=lambda d: os.path.getctime(os.path.join(assets_dir, d)), reverse=True)

    # Return the newest directory
    return os.path.join(assets_dir, subdirs[0])

def check_story_json(story_dir):
    """
    Check the story.json file.

    Args:
        story_dir (str): Path to the story directory

    Returns:
        dict: The story data, or None if not found
    """
    print(f"\nChecking story.json in {story_dir}...")

    story_path = os.path.join(story_dir, 'story.json')
    if not os.path.exists(story_path):
        print(f"❌ {story_path} does not exist")
        return None

    try:
        with open(story_path, 'r', encoding='utf-8') as f:
            story = json.load(f)

        print(f"✅ {story_path} exists and is valid JSON")

        # Check the structure
        if "scenes" not in story:
            print(f"❌ {story_path} does not contain 'scenes' key")
            return story

        scenes = story["scenes"]
        print(f"✅ {story_path} contains {len(scenes)} scenes")

        # Check the first scene
        if len(scenes) > 0:
            first_scene = scenes[0]
            print(f"First scene keys: {list(first_scene.keys())}")

            if "narration" not in first_scene:
                print(f"❌ First scene does not contain 'narration' key")
            else:
                narration = first_scene["narration"]
                print(f"✅ First scene contains narration: {narration[:50]}...")

            if "visual_description" not in first_scene:
                print(f"❌ First scene does not contain 'visual_description' key")
            else:
                visual_desc = first_scene["visual_description"]
                print(f"✅ First scene contains visual description: {visual_desc[:50]}...")

        return story
    except Exception as e:
        print(f"❌ Error reading {story_path}: {str(e)}")
        return None

def check_audio_files(story_dir):
    """
    Check the audio files.

    Args:
        story_dir (str): Path to the story directory

    Returns:
        list: List of audio files
    """
    print(f"\nChecking audio files in {story_dir}...")

    audio_dir = os.path.join(story_dir, 'audio')
    if not os.path.exists(audio_dir):
        print(f"❌ {audio_dir} does not exist")
        return []

    audio_files = [f for f in os.listdir(audio_dir) if f.endswith('.mp3')]
    if not audio_files:
        print(f"❌ No audio files found in {audio_dir}")
        return []

    print(f"✅ Found {len(audio_files)} audio files in {audio_dir}")

    # Sort the files by scene and segment number
    audio_files.sort()

    # Print the first few files
    for i, file in enumerate(audio_files[:5]):
        file_path = os.path.join(audio_dir, file)
        file_size = os.path.getsize(file_path)
        print(f"  {i+1}. {file} ({file_size} bytes)")

    if len(audio_files) > 5:
        print(f"  ... and {len(audio_files) - 5} more")

    return audio_files

def check_image_files(story_dir):
    """
    Check the image files.

    Args:
        story_dir (str): Path to the story directory

    Returns:
        list: List of image files
    """
    print(f"\nChecking image files in {story_dir}...")

    image_dir = os.path.join(story_dir, 'images')
    if not os.path.exists(image_dir):
        print(f"❌ {image_dir} does not exist")
        return []

    image_files = [f for f in os.listdir(image_dir) if f.endswith('.png')]
    if not image_files:
        print(f"❌ No image files found in {image_dir}")
        return []

    print(f"✅ Found {len(image_files)} image files in {image_dir}")

    # Sort the files by scene and segment number
    image_files.sort()

    # Print the first few files
    for i, file in enumerate(image_files[:5]):
        file_path = os.path.join(image_dir, file)
        file_size = os.path.getsize(file_path)
        print(f"  {i+1}. {file} ({file_size} bytes)")

    if len(image_files) > 5:
        print(f"  ... and {len(image_files) - 5} more")

    return image_files

def check_final_video(story_dir):
    """
    Check the final video and its 2x speed version if it exists.

    Args:
        story_dir (str): Path to the story directory

    Returns:
        tuple: (bool, bool) indicating if the normal video and 2x speed video exist
    """
    print(f"\nChecking final videos in {story_dir}...")

    # Check normal speed video
    video_path = os.path.join(story_dir, 'final_video.mp4')
    normal_exists = os.path.exists(video_path)

    if not normal_exists:
        print(f"❌ {video_path} does not exist")
    else:
        file_size = os.path.getsize(video_path)
        print(f"✅ {video_path} exists ({file_size} bytes)")

        # Check if the file size is reasonable (at least 1MB)
        if file_size < 1024 * 1024:
            print(f"⚠️ {video_path} is very small ({file_size} bytes), might be corrupted")

    # Check 2x speed video
    fast_video_path = os.path.join(story_dir, 'final_video_2x.mp4')
    fast_exists = os.path.exists(fast_video_path)

    if not fast_exists:
        print(f"ℹ️ {fast_video_path} does not exist (2x speed version not generated)")
    else:
        file_size = os.path.getsize(fast_video_path)
        print(f"✅ {fast_video_path} exists ({file_size} bytes)")

        # Check if the file size is reasonable (at least 1MB)
        if file_size < 1024 * 1024:
            print(f"⚠️ {fast_video_path} is very small ({file_size} bytes), might be corrupted")

    return (normal_exists, fast_exists)

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Diagnose video generation')
    parser.add_argument('--story-dir', help='Path to the story directory')
    return parser.parse_args()

def main():
    """Main function to run the diagnostics."""
    print("\n" + "="*80)
    print("Video Generation Diagnostic Script")
    print("="*80)

    # Load environment variables
    load_dotenv(override=True)

    # Parse arguments
    args = parse_arguments()

    # Get the story directory
    story_dir = args.story_dir
    if not story_dir:
        story_dir = get_latest_story_dir()
        if not story_dir:
            print("❌ No story directory found. Please run the main.py script first.")
            return

    print(f"Using story directory: {story_dir}")

    # Check the story.json file
    story = check_story_json(story_dir)

    # Check the audio files
    audio_files = check_audio_files(story_dir)

    # Check the image files
    image_files = check_image_files(story_dir)

    # Check the final video
    normal_video_exists, fast_video_exists = check_final_video(story_dir)

    # Print summary
    print("\n" + "="*80)
    print("Summary")
    print("="*80)

    if story:
        scene_count = len(story.get("scenes", []))
        print(f"Story: {scene_count} scenes")
    else:
        print("Story: Not found or invalid")

    print(f"Audio files: {len(audio_files)}")
    print(f"Image files: {len(image_files)}")
    print(f"Normal speed video: {'✅ Present' if normal_video_exists else '❌ Missing'}")
    print(f"2x speed video: {'✅ Present' if fast_video_exists else 'ℹ️ Not generated'}")

    # Check for mismatches
    if story and len(story.get("scenes", [])) != len(audio_files):
        print(f"⚠️ Mismatch: {scene_count} scenes but {len(audio_files)} audio files")

    if story and len(story.get("scenes", [])) != len(image_files):
        print(f"⚠️ Mismatch: {scene_count} scenes but {len(image_files)} image files")

    if len(audio_files) != len(image_files):
        print(f"⚠️ Mismatch: {len(audio_files)} audio files but {len(image_files)} image files")

    print("\n" + "="*80)

if __name__ == "__main__":
    main()
