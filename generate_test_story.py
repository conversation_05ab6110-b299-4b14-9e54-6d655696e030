#!/usr/bin/env python3
"""
Generate a test story for testing the video generation pipeline.
"""

import os
import json
import logging
import datetime
import re
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_story_directory(title):
    """
    Create a unique directory for the story assets.

    Args:
        title (str): The title of the story

    Returns:
        str: Path to the story directory
    """
    # Create a timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

    # Create a safe directory name from the title
    safe_title = re.sub(r'[^\w\s-]', '', title).strip().lower()
    safe_title = re.sub(r'[-\s]+', '_', safe_title)

    # Combine title and timestamp
    dir_name = f"{safe_title}_{timestamp}"

    # Create the full path
    story_dir = os.path.join('assets', dir_name)

    # Create the directory and subdirectories
    os.makedirs(story_dir, exist_ok=True)
    os.makedirs(os.path.join(story_dir, 'audio'), exist_ok=True)
    os.makedirs(os.path.join(story_dir, 'images'), exist_ok=True)

    logger.info(f"Created story directory: {story_dir}")
    return story_dir

def generate_test_story(story_dir):
    """
    Generate a test story with multiple scenes.

    Args:
        story_dir (str): Path to the story directory

    Returns:
        dict: The generated story
    """
    print("\nGenerating test story...")

    # Create a simple test story with 3 scenes
    story = {
        "title": "भूतिया हवेली का रहस्य",
        "scenes": [
            {
                "scene_number": 1,
                "narration": "एक पुरानी हवेली थी, जिसके बारे में कहा जाता था कि वहां भूत रहते हैं। कोई भी उस हवेली के पास नहीं जाता था।",
                "visual_description": "A dilapidated, ancient mansion on a hill, surrounded by overgrown vegetation. The mansion has broken windows, a crumbling facade, and an eerie atmosphere with dark clouds looming overhead."
            },
            {
                "scene_number": 2,
                "narration": "राहुल एक साहसी युवक था। उसने हवेली के रहस्य को जानने का फैसला किया। वह एक अंधेरी रात में हवेली के अंदर गया।",
                "visual_description": "A young man with a flashlight approaching the mansion at night. He looks determined but slightly nervous as he pushes open the creaking front gate."
            },
            {
                "scene_number": 3,
                "narration": "हवेली के अंदर, राहुल ने अजीब आवाजें सुनीं। अचानक, एक तेज़ हवा चली और सभी दरवाजे बंद हो गए। राहुल डर गया।",
                "visual_description": "Inside the mansion, dusty furniture covered with white sheets. Moonlight streams through broken windows, casting eerie shadows. The young man looks startled as doors slam shut around him."
            }
        ]
    }

    # Save the story to a JSON file
    story_path = os.path.join(story_dir, 'story.json')
    with open(story_path, 'w', encoding='utf-8') as f:
        json.dump(story, f, ensure_ascii=False, indent=2)

    print(f"✅ Test story saved to {story_path}")
    return story

def main():
    """Main function to generate the test story."""
    print("\n" + "="*80)
    print("Test Story Generator")
    print("="*80)

    # Load environment variables
    load_dotenv(override=True)

    # Create a unique directory for this story
    title = "भूतिया हवेली का रहस्य"
    story_dir = create_story_directory(title)

    # Generate the test story
    story = generate_test_story(story_dir)

    print("\n" + "="*80)
    print(f"Generated a test story with {len(story['scenes'])} scenes.")
    print("You can now run the main.py script with the following command:")
    print("\n    python main.py --title \"भूतिया हवेली का रहस्य\" --story-type fictional --context \"एक पुरानी हवेली में अजीब घटनाएँ\"")
    print("\nThis will use the pre-generated story instead of creating a new one.")
    print("="*80 + "\n")

if __name__ == "__main__":
    main()
