absl-py==2.2.2
aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aiosignal==1.3.2
alembic==1.15.2
annotated-types==0.7.0
anyascii==0.3.2
anyio==4.9.0
appdirs==1.4.4
asgiref==3.8.1
asttokens==3.0.0
attrs==25.3.0
audioread==3.0.1
auth0-python==4.9.0
babel==2.17.0
backoff==2.2.1
bangla==0.0.5
bcrypt==4.3.0
beautifulsoup4==4.13.4
blinker==1.9.0
blis==1.2.1
bnnumerizer==0.0.2
bnunicodenormalizer==0.1.7
build==1.2.2.post1
cachetools==5.5.2
catalogue==2.0.10
certifi==2025.4.26
cffi==1.17.1
charset-normalizer==3.4.2
chroma-hnswlib==0.7.6
chromadb==0.5.23
click==8.2.0
cloudpathlib==0.21.1
cohere==5.15.0
coloredlogs==15.0.1
confection==0.1.5
contourpy==1.3.2
coqpit==0.0.17
crewai==0.119.0
crewai-tools==0.44.0
cryptography==44.0.3
cycler==0.12.1
cymem==2.0.11
Cython==3.1.1
dataclasses-json==0.6.7
dateparser==1.1.8
decorator==5.2.1
Deprecated==1.2.18
deprecation==2.1.0
distro==1.9.0
docker==7.1.0
docopt==0.6.2
docstring_parser==0.16
durationpy==0.9
einops==0.8.1
elevenlabs==1.58.1
embedchain==0.1.128
encodec==0.1.1
et_xmlfile==2.0.0
executing==2.2.0
fastapi==0.115.9
fastavro==1.10.0
filelock==3.18.0
Flask==3.1.1
flatbuffers==25.2.10
fonttools==4.58.0
frozenlist==1.6.0
fsspec==2025.3.2
g2pkk==0.1.2
google-auth==2.40.1
googleapis-common-protos==1.70.0
gptcache==0.1.44
grpcio==1.71.0
gruut==2.2.3
gruut-ipa==0.13.0
gruut_lang_de==2.0.1
gruut_lang_en==2.0.1
gruut_lang_es==2.0.1
gruut_lang_fr==2.0.2
h11==0.16.0
h2==4.2.0
hangul-romanize==0.1.0
hf-xet==1.1.0
hpack==4.1.0
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.31.1
humanfriendly==10.0
hyperframe==6.1.0
idna==3.10
imageio==2.37.0
imageio-ffmpeg==0.6.0
importlib_metadata==8.6.1
importlib_resources==6.5.2
inflect==7.5.0
instructor==1.8.1
ipython==9.2.0
ipython_pygments_lexers==1.1.1
itsdangerous==2.2.0
jamo==0.4.1
jedi==0.19.2
jieba==0.42.1
Jinja2==3.1.6
jiter==0.8.2
joblib==1.5.0
json5==0.12.0
json_repair==0.44.1
jsonlines==1.2.0
jsonpatch==1.33
jsonpickle==4.0.5
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema==4.23.0
jsonschema-specifications==2025.4.1
kiwisolver==1.4.8
kubernetes==32.0.1
lancedb==0.22.0
langchain==0.3.25
langchain-cohere==0.3.5
langchain-community==0.3.23
langchain-core==0.3.59
langchain-experimental==0.3.4
langchain-openai==0.2.14
langchain-text-splitters==0.3.8
langcodes==3.5.0
langsmith==0.3.42
language_data==1.3.0
lazy_loader==0.4
librosa==0.11.0
litellm==1.68.0
llvmlite==0.44.0
Mako==1.3.10
marisa-trie==1.2.1
Markdown==3.8
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.3
matplotlib-inline==0.1.7
mdurl==0.1.2
mem0ai==0.1.98
mmh3==5.1.0
monotonic==1.6
more-itertools==10.7.0
moviepy==2.1.2
mpmath==1.3.0
msgpack==1.1.0
multidict==6.4.3
murmurhash==1.0.12
mypy_extensions==1.1.0
networkx==2.8.8
nltk==3.9.1
nodeenv==1.9.1
num2words==0.5.14
numba==0.61.2
numpy==1.26.4
oauthlib==3.2.2
onnxruntime==1.22.0
openai==1.75.0
openpyxl==3.1.5
opentelemetry-api==1.33.0
opentelemetry-exporter-otlp-proto-common==1.33.0
opentelemetry-exporter-otlp-proto-grpc==1.33.0
opentelemetry-exporter-otlp-proto-http==1.33.0
opentelemetry-instrumentation==0.54b0
opentelemetry-instrumentation-asgi==0.54b0
opentelemetry-instrumentation-fastapi==0.54b0
opentelemetry-proto==1.33.0
opentelemetry-sdk==1.33.0
opentelemetry-semantic-conventions==0.54b0
opentelemetry-util-http==0.54b0
orjson==3.10.18
overrides==7.7.0
packaging==24.2
pandas==1.5.3
parso==0.8.4
pdfminer.six==20250327
pdfplumber==0.11.6
pexpect==4.9.0
pillow==10.4.0
platformdirs==4.3.8
pooch==1.8.2
portalocker==2.10.1
posthog==3.25.0
preshed==3.0.9
proglog==0.1.12
prompt_toolkit==3.0.51
propcache==0.3.1
protobuf==5.29.4
psutil==7.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
pyarrow==20.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.22
pydantic==2.11.4
pydantic-settings==2.9.1
pydantic_core==2.33.2
Pygments==2.19.1
PyJWT==2.10.1
pynndescent==0.5.13
pyparsing==3.2.3
pypdf==5.5.0
pypdfium2==4.30.1
PyPika==0.48.9
pypinyin==0.54.0
pyproject_hooks==1.2.0
pyright==1.1.400
pysbd==0.3.4
python-crfsuite==0.9.11
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
pytube==15.0.0
pytz==2024.2
pyvis==0.3.2
PyYAML==6.0.2
qdrant-client==1.14.2
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rich==13.9.4
rpds-py==0.24.0
rsa==4.9.1
safetensors==0.5.3
schema==0.7.7
scikit-learn==1.6.1
scipy==1.15.3
shellingham==1.5.4
six==1.17.0
smart-open==7.1.0
sniffio==1.3.1
soundfile==0.13.1
soupsieve==2.7
soxr==0.5.0.post1
spacy==3.8.6
spacy-legacy==3.0.12
spacy-loggers==1.0.5
SQLAlchemy==2.0.40
srsly==2.5.1
stack-data==0.6.3
starlette==0.45.3
SudachiDict-core==20250515
SudachiPy==0.6.10
sympy==1.14.0
tabulate==0.9.0
tenacity==9.1.2
tensorboard==2.19.0
tensorboard-data-server==0.7.2
thinc==8.3.4
threadpoolctl==3.6.0
tiktoken==0.9.0
tokenizers==0.20.3
tomli==2.2.1
tomli_w==1.2.0
torch==2.7.0
torchaudio==2.7.0
tqdm==4.67.1
trainer==0.0.36
traitlets==5.14.3
transformers==4.46.3
typeguard==4.4.2
typer==0.15.3
types-requests==2.32.0.20250328
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.13.2
tzdata==2025.2
tzlocal==5.3.1
umap-learn==0.5.7
Unidecode==1.4.0
urllib3==2.4.0
uv==0.7.3
uvicorn==0.34.2
uvloop==0.21.0
wasabi==1.1.3
watchfiles==1.0.5
wcwidth==0.2.13
weasel==0.4.1
websocket-client==1.8.0
websockets==15.0.1
Werkzeug==3.1.3
wrapt==1.17.2
yarl==1.20.0
zipp==3.21.0
zstandard==0.23.0
