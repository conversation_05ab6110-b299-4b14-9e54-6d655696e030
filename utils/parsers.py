"""
Output Parsers
-------------
Langchain output parsers for structured data in the story generation pipeline.
"""

import re
import json
import logging
from typing import Type, List, Optional

from pydantic import BaseModel
from langchain.prompts import PromptTemplate
from langchain.output_parsers import PydanticOutputParser

from models.schema import (
    Story,
    Scene,
    ImagePrompt,
    SceneSegment,
    ResearchData,
    EnhancedResearchData,
    SceneSegmentList,
    ImagePromptList,
    CharacterConsistencyData,
    Character,
    CharacterReference,
)

logger = logging.getLogger(__name__)


class OutputParser:
    """Base class for output parsers."""

    @staticmethod
    def create_parser(model_class: Type[BaseModel]) -> PydanticOutputParser:
        """Create a Pydantic output parser for the given model class."""
        return PydanticOutputParser(pydantic_object=model_class)

    @staticmethod
    def get_format_instructions(parser: PydanticOutputParser) -> str:
        """Get format instructions for the given parser."""
        return parser.get_format_instructions()

    @staticmethod
    def add_format_instructions_to_prompt(prompt: str, parser: PydanticOutputParser) -> str:
        """Add format instructions to a prompt."""
        format_instructions = parser.get_format_instructions()
        return f"{prompt}\n\n{format_instructions}"

    @staticmethod
    def create_prompt_template(template: str, parser: PydanticOutputParser) -> PromptTemplate:
        """Create a prompt template with format instructions."""
        format_instructions = parser.get_format_instructions()
        template_with_instructions = f"{template}\n\n{format_instructions}"

        # Extract input variables from the template
        input_variables = [
            var.strip('{}') for var in re.findall(r'\{[^{}]+\}', template)
        ]

        return PromptTemplate(
            template=template_with_instructions,
            input_variables=input_variables,
            partial_variables={"format_instructions": format_instructions}
        )

    @staticmethod
    def parse_output(parser: PydanticOutputParser, output: str) -> Optional[BaseModel]:
        """Parse the output using the given parser."""
        try:
            # First try to parse directly
            return parser.parse(output)
        except Exception as e:
            logger.warning(f"Direct parsing failed: {str(e)}")

            # Try to extract JSON from the output
            try:
                # For list output
                if parser.pydantic_object.__name__ == "List":
                    json_match = re.search(r'(\[[\s\S]*\])', output)
                    if json_match:
                        json_str = json_match.group(1)
                        data = json.loads(json_str)
                        return parser.parse(json.dumps(data))
                # For object output
                else:
                    json_match = re.search(r'({[\s\S]*})', output)
                    if json_match:
                        json_str = json_match.group(1)
                        data = json.loads(json_str)
                        return parser.parse(json.dumps(data))
            except Exception as e2:
                logger.warning(f"JSON extraction failed: {str(e2)}")

            # Return None if all parsing attempts fail
            return None


class ResearchDataParser(OutputParser):
    """Parser for research data."""

    @staticmethod
    def create() -> PydanticOutputParser:
        """Create a parser for ResearchData."""
        return OutputParser.create_parser(ResearchData)


class EnhancedResearchDataParser(OutputParser):
    """Parser for enhanced research data."""

    @staticmethod
    def create() -> PydanticOutputParser:
        """Create a parser for EnhancedResearchData."""
        return OutputParser.create_parser(EnhancedResearchData)


class StoryParser(OutputParser):
    """Parser for story data."""

    @staticmethod
    def create() -> PydanticOutputParser:
        """Create a parser for Story."""
        return OutputParser.create_parser(Story)

    @staticmethod
    def create_scene_parser() -> PydanticOutputParser:
        """Create a parser for Scene."""
        return OutputParser.create_parser(Scene)

    @staticmethod
    def parse_scene_output(parser, output: str) -> Optional[Scene]:
        """
        Parse the output using the given parser with enhanced error handling for Scene objects.
        """
        try:
            # First try to parse directly
            return parser.parse(output)
        except Exception as e:
            logger.warning(f"Direct parsing failed: {str(e)}")

            # Try to extract JSON from the output
            try:
                json_match = re.search(r'({[\s\S]*})', output)
                if json_match:
                    json_str = json_match.group(1)
                    data = json.loads(json_str)

                    # Manually create a Scene object
                    try:
                        scene = Scene(
                            scene_number=data.get("scene_number", 1),
                            narration=data.get("narration", ""),
                            visual_description=data.get("visual_description", ""),
                            transition_from_previous=data.get("transition_from_previous", ""),
                            narrative_purpose=data.get("narrative_purpose", "")
                        )
                        return scene
                    except Exception as scene_e:
                        logger.warning(f"Error creating scene: {str(scene_e)}")
            except Exception as e2:
                logger.warning(f"JSON extraction failed: {str(e2)}")

            # Return None if all parsing attempts fail
            return None


class SceneSegmentListParser(OutputParser):
    """Parser for a list of scene segments."""

    @staticmethod
    def create() -> PydanticOutputParser:
        """Create a parser for a list of SceneSegment objects."""
        return OutputParser.create_parser(SceneSegmentList)

    @staticmethod
    def parse_output(parser, output: str) -> Optional[List[SceneSegment]]:
        """
        Parse the output using the given parser with enhanced error handling.

        This overrides the parent class method to provide better handling for list types.
        """
        try:
            # First try to parse directly
            result = parser.parse(output)
            if isinstance(result, SceneSegmentList):
                return result.segments
            return result
        except Exception as e:
            logger.warning(f"Direct parsing failed: {str(e)}")

            # Try to extract JSON from the output
            try:
                # First try to find a complete JSON object
                json_match = re.search(r'({[\s\S]*})', output)
                if json_match:
                    json_str = json_match.group(1)
                    data = json.loads(json_str)

                    # Check if it's a SceneSegmentList format
                    if "segments" in data and isinstance(data["segments"], list):
                        # Manually create SceneSegment objects
                        segments = []
                        for item in data["segments"]:
                            try:
                                segment = SceneSegment(
                                    scene_number=item.get("scene_number", 1),
                                    segment_number=item.get("segment_number", 1),
                                    narration=item.get("narration", ""),
                                    visual_cue=item.get("visual_cue", ""),
                                    estimated_duration_seconds=item.get("estimated_duration_seconds", 20)
                                )
                                segments.append(segment)
                            except Exception as item_e:
                                logger.warning(f"Error creating segment: {str(item_e)}")

                        if segments:
                            return segments

                # If that fails, look for a JSON array directly
                json_match = re.search(r'(\[[\s\S]*\])', output)
                if json_match:
                    json_str = json_match.group(1)
                    data = json.loads(json_str)

                    # Manually create SceneSegment objects
                    segments = []
                    for item in data:
                        try:
                            segment = SceneSegment(
                                scene_number=item.get("scene_number", 1),
                                segment_number=item.get("segment_number", 1),
                                narration=item.get("narration", ""),
                                visual_cue=item.get("visual_cue", ""),
                                estimated_duration_seconds=item.get("estimated_duration_seconds", 20)
                            )
                            segments.append(segment)
                        except Exception as item_e:
                            logger.warning(f"Error creating segment: {str(item_e)}")

                    if segments:
                        return segments
            except Exception as e2:
                logger.warning(f"JSON extraction failed: {str(e2)}")

            # Return None if all parsing attempts fail
            return None


class ImagePromptListParser(OutputParser):
    """Parser for a list of image prompts."""

    @staticmethod
    def create() -> PydanticOutputParser:
        """Create a parser for a list of ImagePrompt objects."""
        return OutputParser.create_parser(ImagePromptList)

    @staticmethod
    def parse_output(parser, output: str) -> Optional[List[ImagePrompt]]:
        """
        Parse the output using the given parser with enhanced error handling.

        This overrides the parent class method to provide better handling for list types.
        """
        try:
            # First try to parse directly
            result = parser.parse(output)
            if isinstance(result, ImagePromptList):
                return result.prompts
            return result
        except Exception as e:
            logger.warning(f"Direct parsing failed: {str(e)}")

            # Try to extract JSON from the output
            try:
                # First try to find a complete JSON object
                json_match = re.search(r'({[\s\S]*})', output)
                if json_match:
                    json_str = json_match.group(1)
                    data = json.loads(json_str)

                    # Check if it's an ImagePromptList format
                    if "prompts" in data and isinstance(data["prompts"], list):
                        # Manually create ImagePrompt objects
                        prompts = []
                        for item in data["prompts"]:
                            try:
                                prompt = ImagePrompt(
                                    scene_number=item.get("scene_number", 1),
                                    segment_number=item.get("segment_number", 1),
                                    narration=item.get("narration", ""),
                                    visual_cue=item.get("visual_cue", ""),
                                    image_prompt=item.get("image_prompt", ""),
                                    estimated_duration_seconds=item.get("estimated_duration_seconds", 20)
                                )
                                prompts.append(prompt)
                            except Exception as item_e:
                                logger.warning(f"Error creating image prompt: {str(item_e)}")

                        if prompts:
                            return prompts

                # If that fails, look for a JSON array directly
                json_match = re.search(r'(\[[\s\S]*\])', output)
                if json_match:
                    json_str = json_match.group(1)
                    data = json.loads(json_str)

                    # Manually create ImagePrompt objects
                    prompts = []
                    for item in data:
                        try:
                            prompt = ImagePrompt(
                                scene_number=item.get("scene_number", 1),
                                segment_number=item.get("segment_number", 1),
                                narration=item.get("narration", ""),
                                visual_cue=item.get("visual_cue", ""),
                                image_prompt=item.get("image_prompt", ""),
                                estimated_duration_seconds=item.get("estimated_duration_seconds", 20)
                            )
                            prompts.append(prompt)
                        except Exception as item_e:
                            logger.warning(f"Error creating image prompt: {str(item_e)}")

                    if prompts:
                        return prompts
            except Exception as e2:
                logger.warning(f"JSON extraction failed: {str(e2)}")

            # Return None if all parsing attempts fail
            return None


class CharacterConsistencyDataParser(OutputParser):
    """Parser for character consistency data."""

    @staticmethod
    def create() -> PydanticOutputParser:
        """Create a parser for CharacterConsistencyData."""
        return OutputParser.create_parser(CharacterConsistencyData)

    @staticmethod
    def parse_output(parser, output: str) -> Optional[CharacterConsistencyData]:
        """
        Parse the output using the given parser with enhanced error handling for CharacterConsistencyData.
        """
        try:
            # First try to parse directly
            return parser.parse(output)
        except Exception as e:
            logger.warning(f"Direct parsing failed: {str(e)}")

            # Try to extract JSON from the output
            try:
                json_match = re.search(r'({[\s\S]*})', output)
                if json_match:
                    json_str = json_match.group(1)
                    data = json.loads(json_str)

                    # Manually create CharacterConsistencyData object
                    try:
                        # Parse characters
                        characters = []
                        if "characters" in data and isinstance(data["characters"], list):
                            for char_data in data["characters"]:
                                try:
                                    character = Character(
                                        name=char_data.get("name", "Unknown"),
                                        role=char_data.get("role", "supporting character"),
                                        physical_description=char_data.get("physical_description", ""),
                                        clothing_style=char_data.get("clothing_style", ""),
                                        distinctive_features=char_data.get("distinctive_features", ""),
                                        personality_traits=char_data.get("personality_traits", ""),
                                        background=char_data.get("background", ""),
                                        scenes_appeared=char_data.get("scenes_appeared", [])
                                    )
                                    characters.append(character)
                                except Exception as char_e:
                                    logger.warning(f"Error creating character: {str(char_e)}")

                        # Parse character references (usually empty initially)
                        character_references = []
                        if "character_references" in data and isinstance(data["character_references"], list):
                            for ref_data in data["character_references"]:
                                try:
                                    reference = CharacterReference(
                                        character_name=ref_data.get("character_name", ""),
                                        reference_image_path=ref_data.get("reference_image_path", ""),
                                        image_prompt_used=ref_data.get("image_prompt_used", ""),
                                        generation_timestamp=ref_data.get("generation_timestamp", "")
                                    )
                                    character_references.append(reference)
                                except Exception as ref_e:
                                    logger.warning(f"Error creating character reference: {str(ref_e)}")

                        # Parse character appearance notes
                        character_appearance_notes = data.get("character_appearance_notes", {})
                        if not isinstance(character_appearance_notes, dict):
                            character_appearance_notes = {}

                        # Create the CharacterConsistencyData object
                        consistency_data = CharacterConsistencyData(
                            characters=characters,
                            character_references=character_references,
                            character_appearance_notes=character_appearance_notes
                        )
                        return consistency_data

                    except Exception as consistency_e:
                        logger.warning(f"Error creating character consistency data: {str(consistency_e)}")
            except Exception as e2:
                logger.warning(f"JSON extraction failed: {str(e2)}")

            # Return empty data if all parsing attempts fail
            logger.warning("All parsing attempts failed, returning empty CharacterConsistencyData")
            return CharacterConsistencyData(
                characters=[],
                character_references=[],
                character_appearance_notes={}
            )