"""
Rate Limited LLM
---------------
Custom LLM implementation for CrewAI that handles rate limiting and implements
exponential backoff for OpenAI API calls.
"""

import os
import json
import time
import logging
from typing import Any, Dict, List, Optional, Union, Callable

import litellm
from crewai import BaseLLM
from litellm.exceptions import RateLimitError, ServiceUnavailableError, APIError

logger = logging.getLogger(__name__)

class RateLimitedLLM(BaseLLM):
    """
    A custom LLM implementation for CrewAI that handles rate limiting and implements
    exponential backoff for OpenAI API calls.
    """
    
    def __init__(
        self,
        model: str = "gpt-4o",
        temperature: float = 0.7,
        api_key: Optional[str] = None,
        max_retries: int = 5,
        initial_retry_delay: float = 1.0,
        max_tokens_per_minute: int = 30000,
        token_bucket_size: int = 40000,
        **kwargs
    ):
        """
        Initialize the rate-limited LLM.
        
        Args:
            model (str): The OpenAI model to use. Defaults to "gpt-4o".
            temperature (float): The temperature for generation. Defaults to 0.7.
            api_key (str, optional): The OpenAI API key. If not provided, will use OPENAI_API_KEY env var.
            max_retries (int): Maximum number of retries for rate-limited requests. Defaults to 5.
            initial_retry_delay (float): Initial delay in seconds before retrying. Defaults to 1.0.
            max_tokens_per_minute (int): Maximum tokens per minute to use. Defaults to 30000.
            token_bucket_size (int): Size of the token bucket for rate limiting. Defaults to 40000.
            **kwargs: Additional arguments to pass to the LLM.
        """
        super().__init__(model, temperature)
        
        self.model = model
        self.temperature = temperature
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.max_retries = max_retries
        self.initial_retry_delay = initial_retry_delay
        self.max_tokens_per_minute = max_tokens_per_minute
        self.token_bucket_size = token_bucket_size
        self.kwargs = kwargs
        
        # Rate limiting state
        self.tokens_used = 0
        self.last_refill_time = time.time()
        self.token_bucket = token_bucket_size
        self.tokens_per_second = max_tokens_per_minute / 60.0
        
        # Validate API key
        if not self.api_key:
            raise ValueError("OpenAI API key is required. Set it via the api_key parameter or OPENAI_API_KEY environment variable.")
        
        logger.info(f"Initialized RateLimitedLLM with model {model}, max_tokens_per_minute: {max_tokens_per_minute}")
    
    def _refill_token_bucket(self):
        """Refill the token bucket based on elapsed time."""
        now = time.time()
        elapsed = now - self.last_refill_time
        self.last_refill_time = now
        
        # Calculate tokens to add based on elapsed time
        tokens_to_add = elapsed * self.tokens_per_second
        self.token_bucket = min(self.token_bucket + tokens_to_add, self.token_bucket_size)
    
    def _consume_tokens(self, tokens: int) -> bool:
        """
        Try to consume tokens from the bucket.
        
        Args:
            tokens (int): Number of tokens to consume
            
        Returns:
            bool: True if tokens were consumed, False if not enough tokens
        """
        self._refill_token_bucket()
        
        if tokens <= self.token_bucket:
            self.token_bucket -= tokens
            self.tokens_used += tokens
            return True
        
        return False
    
    def _wait_for_tokens(self, tokens: int):
        """
        Wait until enough tokens are available.
        
        Args:
            tokens (int): Number of tokens needed
        """
        while True:
            self._refill_token_bucket()
            
            if tokens <= self.token_bucket:
                self.token_bucket -= tokens
                self.tokens_used += tokens
                return
            
            # Calculate how long to wait for enough tokens
            tokens_needed = tokens - self.token_bucket
            wait_time = tokens_needed / self.tokens_per_second
            
            # Add a small buffer to ensure we have enough tokens
            wait_time = min(wait_time * 1.1, 60)  # Cap at 60 seconds
            
            logger.info(f"Rate limiting: waiting {wait_time:.2f}s for {tokens_needed:.0f} tokens")
            time.sleep(wait_time)
    
    def _estimate_tokens(self, messages: Union[str, List[Dict[str, str]]]) -> int:
        """
        Estimate the number of tokens in the messages.
        
        Args:
            messages: The messages to estimate tokens for
            
        Returns:
            int: Estimated token count
        """
        # Simple estimation based on characters
        if isinstance(messages, str):
            return len(messages) // 4
        
        # For message list, estimate based on content
        total_chars = 0
        for message in messages:
            content = message.get("content", "")
            if isinstance(content, str):
                total_chars += len(content)
            elif isinstance(content, list):  # Handle multimodal content
                for item in content:
                    if isinstance(item, dict) and "text" in item:
                        total_chars += len(item["text"])
        
        # Rough estimate: 4 characters per token
        return total_chars // 4
    
    def call(
        self,
        messages: Union[str, List[Dict[str, str]]],
        tools: Optional[List[dict]] = None,
        callbacks: Optional[List[Any]] = None,
        available_functions: Optional[Dict[str, Callable]] = None,
    ) -> Union[str, Any]:
        """
        Call the LLM with the given messages, implementing retry logic and rate limiting.
        
        Args:
            messages: Input messages for the LLM
            tools: Optional list of tool schemas for function calling
            callbacks: Optional list of callback functions
            available_functions: Optional dict mapping function names to callables
            
        Returns:
            Either a text response from the LLM or the result of a tool function call
            
        Raises:
            Exception: If the LLM call fails after all retries
        """
        # Convert string message to proper format if needed
        if isinstance(messages, str):
            messages = [{"role": "user", "content": messages}]
        
        # Estimate token usage
        estimated_tokens = self._estimate_tokens(messages)
        
        # Wait for enough tokens to be available
        self._wait_for_tokens(estimated_tokens)
        
        # Prepare litellm arguments
        litellm_args = {
            "model": self.model,
            "messages": messages,
            "temperature": self.temperature,
            "api_key": self.api_key,
        }
        
        # Add tools if provided
        if tools:
            litellm_args["tools"] = tools
        
        # Add any additional kwargs
        litellm_args.update(self.kwargs)
        
        # Implement retry logic with exponential backoff
        for attempt in range(self.max_retries + 1):
            try:
                response = litellm.completion(**litellm_args)
                
                # Handle function calling if needed
                if available_functions and response.choices[0].message.get("tool_calls"):
                    # Process function calls and return results
                    return self._process_function_calls(response, messages, tools, callbacks, available_functions)
                
                # Return the text response
                return response.choices[0].message.content
                
            except RateLimitError as e:
                if attempt < self.max_retries:
                    # Calculate backoff time
                    backoff_time = self.initial_retry_delay * (2 ** attempt)
                    
                    # Extract rate limit reset time if available
                    reset_time = None
                    if hasattr(e, 'headers') and e.headers and 'x-ratelimit-reset-tokens' in e.headers:
                        try:
                            reset_time = float(e.headers['x-ratelimit-reset-tokens'])
                            backoff_time = max(backoff_time, reset_time)
                        except (ValueError, TypeError):
                            pass
                    
                    logger.warning(f"Rate limit exceeded (attempt {attempt+1}/{self.max_retries}). "
                                  f"Retrying in {backoff_time:.2f}s. Error: {str(e)}")
                    time.sleep(backoff_time)
                else:
                    logger.error(f"Rate limit exceeded after {self.max_retries} retries. Error: {str(e)}")
                    raise
            
            except (ServiceUnavailableError, APIError) as e:
                if attempt < self.max_retries:
                    backoff_time = self.initial_retry_delay * (2 ** attempt)
                    logger.warning(f"API error (attempt {attempt+1}/{self.max_retries}). "
                                  f"Retrying in {backoff_time:.2f}s. Error: {str(e)}")
                    time.sleep(backoff_time)
                else:
                    logger.error(f"API error after {self.max_retries} retries. Error: {str(e)}")
                    raise
            
            except Exception as e:
                logger.error(f"Unexpected error calling LLM: {str(e)}")
                raise
    
    def _process_function_calls(
        self,
        response: Any,
        messages: List[Dict[str, str]],
        tools: Optional[List[dict]],
        callbacks: Optional[List[Any]],
        available_functions: Dict[str, Callable],
    ) -> Any:
        """Process function calls from the LLM response."""
        tool_calls = response.choices[0].message.get("tool_calls", [])
        
        for tool_call in tool_calls:
            function_name = tool_call["function"]["name"]
            function_args = json.loads(tool_call["function"]["arguments"])
            
            if function_name in available_functions:
                function_to_call = available_functions[function_name]
                function_response = function_to_call(**function_args)
                
                # Add the function response to the messages
                messages.append({
                    "role": "tool",
                    "tool_call_id": tool_call["id"],
                    "name": function_name,
                    "content": str(function_response)
                })
        
        # Call the LLM again with the updated messages
        return self.call(messages, tools, callbacks, available_functions)
    
    def supports_function_calling(self) -> bool:
        """Check if the LLM supports function calling."""
        return True
    
    def supports_stop_words(self) -> bool:
        """Check if the LLM supports stop words."""
        return True
    
    def get_context_window_size(self) -> int:
        """Get the context window size of the LLM."""
        # Default context window sizes for common models
        context_windows = {
            "gpt-4o": 128000,
            "gpt-4o-mini": 128000,
            "gpt-4": 8192,
            "gpt-4-32k": 32768,
            "gpt-3.5-turbo": 16385,
            "gpt-3.5-turbo-16k": 16385,
        }
        
        # Return the context window size for the model, or a default value
        return context_windows.get(self.model, 8192)
