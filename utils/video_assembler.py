"""
Video Assembler
-------------
Assembles images and audio into a final video using FFmpeg.
Supports creating a 2x speed version of the video for quick review.
"""

import os
import re
import json
import logging
import tempfile
import subprocess

# Initialize logger
logger = logging.getLogger(__name__)

class VideoAssembler:
    def __init__(self, output_path=None, create_fast_version=False):
        """
        Initialize the video assembler.

        Args:
            output_path (str, optional): Path to save the final video. Defaults to "assets/final_video.mp4".
            create_fast_version (bool, optional): Whether to create a 2x speed version of the video. Defaults to False.
        """
        self.output_path = output_path or "assets/final_video.mp4"
        self.create_fast_version = create_fast_version

        # Check if FFmpeg is installed
        try:
            result = subprocess.run(['ffmpeg', '-version'],
                                   stdout=subprocess.PIPE,
                                   stderr=subprocess.PIPE,
                                   text=True,
                                   check=False)
            if result.returncode == 0:
                logger.info("FFmpeg is available")
            else:
                logger.warning("FFmpeg may not be properly installed")
        except Exception as e:
            logger.error(f"Error checking FFmpeg: {str(e)}")
            logger.warning("FFmpeg may not be installed, video assembly might fail")

    def _get_audio_duration(self, audio_path):
        """
        Get the duration of an audio file using FFmpeg.

        Args:
            audio_path (str): Path to the audio file

        Returns:
            float: Duration in seconds
        """
        try:
            # Use FFmpeg to get the duration
            cmd = [
                'ffprobe',
                '-v', 'error',
                '-show_entries', 'format=duration',
                '-of', 'json',
                audio_path
            ]

            result = subprocess.run(cmd,
                                   stdout=subprocess.PIPE,
                                   stderr=subprocess.PIPE,
                                   text=True,
                                   check=False)

            if result.returncode == 0:
                data = json.loads(result.stdout)
                duration = float(data['format']['duration'])
                logger.info(f"Audio duration for {audio_path}: {duration} seconds")
                return duration
            else:
                logger.error(f"FFprobe error: {result.stderr}")
                return 5.0  # Default duration
        except Exception as e:
            logger.error(f"Error getting audio duration for {audio_path}: {str(e)}")
            return 5.0  # Default duration

    def assemble(self, audio_paths, image_paths, output_path=None):
        """
        Assemble images and audio into a final video using FFmpeg.

        Args:
            audio_paths (list): List of audio file paths
            image_paths (list): List of image file paths
            output_path (str, optional): Path to save the final video. Overrides the path set in __init__.

        Returns:
            str: Path to the final video
        """
        # Update output path if provided
        if output_path:
            self.output_path = output_path
        logger.info("Starting video assembly")

        # Check if we have any audio and image files
        if not audio_paths:
            logger.error("No audio files provided")
            return None

        if not image_paths:
            logger.error("No image files provided")
            return None

        # Log the files we're working with
        logger.info(f"Audio files: {audio_paths}")
        logger.info(f"Image files: {image_paths}")

        if len(audio_paths) != len(image_paths):
            logger.warning(f"Number of audio files ({len(audio_paths)}) does not match number of images ({len(image_paths)})")
            # Use the minimum number
            min_count = min(len(audio_paths), len(image_paths))
            audio_paths = audio_paths[:min_count]
            image_paths = image_paths[:min_count]
            logger.info(f"Using {min_count} matched pairs of audio and image files")

        # Sort the paths by scene and segment number
        def extract_numbers(path):
            # Extract scene and segment numbers from the filename
            match = re.search(r'scene_(\d+)_segment_(\d+)', path)
            if match:
                return (int(match.group(1)), int(match.group(2)))
            return (0, 0)

        # Sort the paths and log them for debugging
        audio_paths.sort(key=extract_numbers)
        image_paths.sort(key=extract_numbers)

        logger.info(f"Sorted audio paths: {audio_paths}")
        logger.info(f"Sorted image paths: {image_paths}")

        # Create a temporary directory for intermediate files
        with tempfile.TemporaryDirectory() as temp_dir:
            logger.info(f"Created temporary directory: {temp_dir}")

            # Create individual video segments
            segment_paths = []

            for i, (audio_path, image_path) in enumerate(zip(audio_paths, image_paths)):
                logger.info(f"Processing segment {i+1}/{len(audio_paths)}")

                # Extract scene and segment info for logging
                scene_match = re.search(r'scene_(\d+)_segment_(\d+)', os.path.basename(audio_path))
                if scene_match:
                    scene_num = scene_match.group(1)
                    segment_num = scene_match.group(2)
                    logger.info(f"Processing scene {scene_num}, segment {segment_num}")

                # Verify the audio file exists and has content
                if not os.path.exists(audio_path):
                    logger.error(f"Audio file {audio_path} does not exist")
                    continue

                if os.path.getsize(audio_path) == 0:
                    logger.error(f"Audio file {audio_path} is empty")
                    continue

                # Verify the image file exists and has content
                if not os.path.exists(image_path):
                    logger.error(f"Image file {image_path} does not exist")
                    continue

                if os.path.getsize(image_path) == 0:
                    logger.error(f"Image file {image_path} is empty")
                    continue

                # Get audio duration
                audio_duration = self._get_audio_duration(audio_path)
                logger.info(f"Audio duration: {audio_duration} seconds")

                # Create segment output path
                segment_path = os.path.join(temp_dir, f"segment_{i:03d}.mp4")

                # Create a video from the image and audio using FFmpeg
                cmd = [
                    'ffmpeg',
                    '-y',  # Overwrite output files
                    '-loop', '1',  # Loop the image
                    '-i', image_path,  # Input image
                    '-i', audio_path,  # Input audio
                    '-c:v', 'libx264',  # Video codec
                    '-tune', 'stillimage',  # Optimize for still image
                    '-c:a', 'aac',  # Audio codec
                    '-b:a', '192k',  # Audio bitrate
                    '-pix_fmt', 'yuv420p',  # Pixel format
                    '-shortest',  # End when the shortest input ends (audio)
                    '-t', str(audio_duration),  # Duration
                    segment_path  # Output file
                ]

                logger.info(f"Running FFmpeg command: {' '.join(cmd)}")

                try:
                    result = subprocess.run(cmd,
                                          stdout=subprocess.PIPE,
                                          stderr=subprocess.PIPE,
                                          text=True,
                                          check=False)

                    if result.returncode == 0:
                        logger.info(f"Successfully created segment: {segment_path}")
                        segment_paths.append(segment_path)
                    else:
                        logger.error(f"FFmpeg error: {result.stderr}")
                except Exception as e:
                    logger.error(f"Error creating segment {i+1}: {str(e)}")

            # Check if we have any segments to concatenate
            if not segment_paths:
                logger.error("No segments created, cannot assemble video")
                return None

            # Create a concat file for FFmpeg
            concat_file_path = os.path.join(temp_dir, "concat.txt")
            with open(concat_file_path, 'w') as f:
                for segment_path in segment_paths:
                    f.write(f"file '{segment_path}'\n")

            logger.info(f"Created concat file with {len(segment_paths)} segments")

            # Concatenate all segments into the final video
            cmd = [
                'ffmpeg',
                '-y',  # Overwrite output files
                '-f', 'concat',  # Format is concat
                '-safe', '0',  # Don't require safe filenames
                '-i', concat_file_path,  # Input file list
                '-c', 'copy',  # Copy streams without re-encoding
                self.output_path  # Output file
            ]

            logger.info(f"Running FFmpeg concat command: {' '.join(cmd)}")

            try:
                result = subprocess.run(cmd,
                                      stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE,
                                      text=True,
                                      check=False)

                if result.returncode == 0:
                    logger.info(f"Video assembly completed successfully, saved to {self.output_path}")

                    # Create a 2x speed version if requested
                    if self.create_fast_version:
                        fast_version_path = self._create_fast_version(self.output_path)
                        if fast_version_path:
                            logger.info(f"Created 2x speed version at {fast_version_path}")
                            # Return both paths as a tuple
                            return (self.output_path, fast_version_path)

                    return self.output_path
                else:
                    logger.error(f"FFmpeg concat error: {result.stderr}")
                    return None
            except Exception as e:
                logger.error(f"Error concatenating segments: {str(e)}")
                return None

    def _create_fast_version(self, input_video_path):
        """
        Create a 2x speed version of the video.

        Args:
            input_video_path (str): Path to the input video

        Returns:
            str: Path to the 2x speed video, or None if creation failed
        """
        # Generate output path by adding "_2x" before the extension
        base, ext = os.path.splitext(input_video_path)
        fast_version_path = f"{base}_2x{ext}"

        logger.info(f"Creating 2x speed version of the video at {fast_version_path}")

        # FFmpeg command to create 2x speed video while preserving audio pitch
        cmd = [
            'ffmpeg',
            '-y',  # Overwrite output files
            '-i', input_video_path,  # Input video
            '-filter_complex', '[0:v]setpts=0.5*PTS[v];[0:a]atempo=2.0[a]',  # Speed up video and audio
            '-map', '[v]',  # Map the video stream
            '-map', '[a]',  # Map the audio stream
            '-c:v', 'libx264',  # Video codec
            '-c:a', 'aac',  # Audio codec
            '-b:a', '192k',  # Audio bitrate
            fast_version_path  # Output file
        ]

        logger.info(f"Running FFmpeg command: {' '.join(cmd)}")

        try:
            result = subprocess.run(cmd,
                                  stdout=subprocess.PIPE,
                                  stderr=subprocess.PIPE,
                                  text=True,
                                  check=False)

            if result.returncode == 0:
                logger.info(f"Successfully created 2x speed version: {fast_version_path}")
                return fast_version_path
            else:
                logger.error(f"FFmpeg error creating 2x speed version: {result.stderr}")
                return None
        except Exception as e:
            logger.error(f"Error creating 2x speed version: {str(e)}")
            return None
